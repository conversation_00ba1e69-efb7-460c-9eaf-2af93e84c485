<div id="movegroup" class="tab-pane fade">
	<div class="tab-body">
		<div class="content">

			<h3>
				Generate Movegroup Validation Sheet from VM Mapping sheet
				<span class="text" style="color: #009925;"> after migration</span>
			</h3>
			<div>
				<div class="tab">
					<button class="tablinks" onclick="openCity(event, 'aws_move_req')"
						id="defaultOpen">Requirements</button>
					<div id="aws_move_req" class="tabcontent">
						<p>
							1. Require <span class="text" style="color:black; font-weight: bold">VM mapping
								sheet</span> filled with move groups assigned. <br>
							2. <span class="text" style="color:black; font-weight: bold">Migration</span>
							and <span class="text" style="color:black; font-weight: bold">VM build
								activity</span> done so that the validation can perform comparison of VM
							mapping sheet data against VMs present in AWS for a given movegroup.<br>
							3. Valid <span class="text" style="color:black; font-weight: bold">AWS ARN</span> 
							with appropriate permissions to access EC2 instances.
						</p>
					</div>
					<button class="tablinks" onclick="openCity(event, 'aws_move_steps')">Steps</button>
					<div id="aws_move_steps" class="tabcontent">

						<p>1. Enter Movegroup name in the VM mapping sheet to be checked.<br>
							2. Enter AWS ARN with appropriate permissions to access EC2 instances.<br>
							3. Optionally specify AWS regions (comma-separated) or leave blank for common regions.<br>
							4. Upload latest copy of VM Mapping sheet.<br>
							5. Click upload button.<br>
							6. An Excel file will be downloaded on your computer after some time.<br>

						</p>

					</div>
					<button class="tablinks" onclick="openCity(event, 'aws_move_out')">Output</button>
					<div id="aws_move_out" class="tabcontent">
						<p>Similar to GCP validation, but with AWS-specific fields like Account, Availability Zone, Security Groups, etc.</p>
						<div class="img-div">
							<img src="./static/assets/images/move_val.png"
								style="margin-left: 0%; margin-top: -8%" width="100%" height="80%">
						</div>
					</div>
					<button class="tablinks" onclick="openCity(event, 'aws_move_vid')">Video</button>
					<div id="aws_move_vid" class="tabcontent">
						<iframe
							src="https://drive.google.com/file/d/1zgLV5r2PG09IyexNxpZDr_hgFI0kCKy7/preview"
							width="690" height="420" allow="autoplay; fullscreen;"></iframe>
					</div>
					<button class="tablinks" onclick="openCity(event, 'aws_move_wn')">What Next?</button>
					<div id="aws_move_wn" class="tabcontent">
						<p>
							The output file will show non-compliance of the AWS infrastructure.
							You may want to take corrective action and re-run the tool again.
						</p>
					</div>
				</div>
				<div class="form-content">
					<div class="card-body"
						style="border-radius:5px; float: left; width : 100%; height : 500px;">
						<form id="form-aws-ggreen" action="aws_move_val" method="post" enctype="multipart/form-data"
							onsubmit="addUUID(this);  revealText(this, 'aws-ggreen','');">
							<div class="form-group" style="padding-right:0px;margin-top: 10px;">
								<span class="span-form-group">Movegroup Name</span>
								<input type="text" placeholder="Enter Movegroup Name"
									class="form-group-class" id="aws-move-group" style="border-radius:5px;"
									name="move-group" pattern="[a-z0-9]+" required><br><br><br>
								<span class="span-form-group">AWS ARN</span>
								<input type="text" placeholder="Enter AWS ARN (e.g., arn:aws:iam::123456789012:role/YourRole)"
									class="form-group-class" id="arn" style="border-radius:5px;"
									name="arn" required><br><br>
								<span class="span-form-group">Regions (Optional)</span>
								<input type="text" placeholder="Enter regions (comma-separated) or leave blank for common regions"
									class="form-group-class" id="regions" style="border-radius:5px;"
									name="regions"><br><br>
								<input type="file" id="aws-excel-file" style="border-radius:5px;"
									name="excel-file" accept=".xlsx" required><br><br><br>
								&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;
								&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;<button id="aws-vall" type="submit"
									class="btn arrow-btn blue-btn"
									style="border-radius:5px;margin-left: -216px;margin-top: -25px;margin-bottom: 15px; background-color: #009925;">Upload</button>
							</div>
						</form>
						<div id="text-block-container-aws-ggreen" style="filter:none"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
