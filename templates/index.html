{% extends 'base.html' %}
{% block main %}
<!--inventory page division-->
<div id="div1" class="tab-pane fade in active show" xmlns="http://www.w3.org/1999/html">
    <div class="nav-container wow fadeInUp">
        <ul class="nav nav-tabs">
            <li>
                <a class="active" data-toggle="tab" href="#software">
                    <img src="./static/assets/images/azinv1.png" />
                    <span>GCP Inventory</span>
                </a>
            </li>
            <li>
                <a data-toggle="tab" href="#aws1">
                    <img src="./static/assets/images/awsinv1.png" />
                    <span>AWS Inventory</span>
                </a>
            </li>
            <li>
                <a data-toggle="tab" href="#ari">
                    <img src="./static/assets/images/inv1.png" />
                    <span>Azure Inventory</span>
                </a>
            </li>
            <li>
                <a data-toggle="tab" href="#oci_inv">
                    <img src="./static/assets/images/oci_inv1.png" />
                    <span>Oracle Inventory</span>
                </a>
            </li>
            <li>
                <a data-toggle="tab" href="#gke_dragon">
                    <img src="./static/assets/images/google-gke.svg" />
                    <span>GKE Inventory</span>
                </a>
            </li>
			<li>
				<a data-toggle="tab" href="#eks">
					<img src="./static/assets/images/eksinv.png" />
					<span>EKS Inventory</span>
				</a>
			</li>
			<li>
				<a data-toggle="tab" href="#looker">
					<img src="./static/assets/images/dashboard.png" style="width: 30px; height: 28px" />
					<span>Looker</span>
				</a>
			</li>
        </ul>
    </div>
    <div class="container">
        <div class="tab-content section-padding wow fadeInUp">
            {% include 'inventory/_gcp_inventory.html' %}
            {% include 'inventory/_gke_inventory.html' %}
            {% include 'inventory/_aws_inventory.html' %}
            {% include 'inventory/_oci_inventory.html' %}
            {% include 'inventory/_eks_inventory.html' %}
            {% include 'inventory/_azure_inventory.html' %}
            {% include 'inventory/_looker.html' %}
        </div>
    </div>
</div>

<!--Compliance page division-->
<div id="div2" class="hidden">
    <div class="nav-container wow fadeInUp">
        <ul class="nav nav-tabs">
            <li>
                <a class="active" data-toggle="tab" href="#location">
                    <img style="width: 35px;" src="./static/assets/images/gcpicon.png" />
                    <span>GCP Compliance</span>
                </a>
            </li>
            <li>
                <a data-toggle="tab" href="#aws2">
                    <img style="width: 35px;" src="./static/assets/images/awslogo1.png" />
                    <span>AWS Compliance</span>
                </a>
            </li>
            <li>
                <a data-toggle="tab" href="#azc">
                    <img src="./static/assets/images/azureicon.png" />
                    <span>Azure Compliance</span>
                </a>
            </li>
            {% if provider == 'gcp' %}
            <li>
                <a data-toggle="tab" href="#work">
                    <img src="./static/assets/images/firewall_logs_logo.png" />
                    <span>Firewall Summary</span>
                </a>
            </li>
            <li>
                <a data-toggle="tab" href="#vpcsc">
                    <img src="./static/assets/images/vpcsc.png" />
                    <span>VPC SC Rules</span>
                </a>
            </li>
            {% endif %}
        </ul>
    </div>

    <div class="container">
        <div class="tab-content section-padding wow fadeInUp">
            {% include 'compliance/_gcp_compliance.html' %}
            {% include 'compliance/_aws_compliance.html' %}
            {% include 'compliance/_azure_compliance.html' %}
        </div>
        {% include 'compliance/_firewall_summary.html' %}
        {% include 'compliance/_vpc_sc_rules.html' %}
    </div>
</div>

</div>

<!--Assessment page division-->
<div id="div3" class="hidden">
    <div class="nav-container wow fadeInUp">
        <ul class="nav nav-tabs">
            {% if provider == 'gcp' %}
            <li>
                <a class="active" data-toggle="tab" href="#aws3">
                    <img style="width: 35px;" src="./static/assets/images/awslogo1.png" />
                    <span>Migration Center</span>
                </a>
            </li>
            <li>
                <a data-toggle="tab" href="#azs">
                    <img src="./static/assets/images/azureicon.png" />
                    <span>Migration Center</span>
                </a>
            </li>
            {% endif %}
            <li>
                <a data-toggle="tab" href="#vmware">
                    <img src="./static/assets/images/vmwaresheet.png" />
                    <span>VMWare sheet</span>
                </a>
            </li>
            <li>
                <a data-toggle="tab" href="#mans">
                    <img style="width: 40px; height: 28px" src="./static/assets/images/mans.png" />
                    <span>Manual Assessment</span>
                </a>
            </li>
            <li>
                <a data-toggle="tab" href="#mansd">
                    <img style="width: 30px; height: 28px" src="./static/assets/images/dashboard.png" />
                    <span>Dashboard</span>
                </a>
            </li>
            {% if provider == 'gcp' %}
            <li>
                <a data-toggle="tab" href="#awsb">
                    <img style="width: 30px; height: 28px" src="./static/assets/images/invoice-d.png" />
                    <span>AWS Scoping</span>
                </a>
            </li>
             <li>
                <a data-toggle="tab" href="#azureb">
                    <img style="width: 30px; height: 28px" src="./static/assets/images/invoice-d.png" />
                    <span>AZURE Scoping</span>
                </a>
            </li>
            {% else %}
            <li>
                <a data-toggle="tab" href="#gcpb">
                    <img style="width: 30px; height: 28px" src="./static/assets/images/invoice-d.png" />
                    <span>GCP Scoping</span>
                </a>
            </li>
            <li>
                <a data-toggle="tab" href="#azureaws">
                    <img style="width: 30px; height: 28px" src="./static/assets/images/invoice-d.png" />
                    <span>AZURE Scoping</span>
                </a>
            </li>
            {% endif %}

        </ul>
    </div>
    <div class="container">
        <div class="tab-content section-padding wow fadeInUp">
            {% include 'assessment/_aws_migration_center.html' %}
            {% include 'assessment/_azure_migration_center.html' %}
            {% include 'assessment/_vmware_sheet.html' %}
            {% include 'assessment/_manual_assessment.html' %}
            {% include 'assessment/_dashboard.html' %}
            {% include 'assessment/_aws_scoping.html' %}
            {% include 'assessment/_gcp_scoping.html' %}
            {% include 'assessment/_azure_scoping.html' %}
        </div>
    </div>
</div>

<!--Migration page division-->
<div id="div4" class="hidden">
    <div class="nav-container wow fadeInUp">
        <ul class="nav nav-tabs">
            {% if provider == 'gcp' %}
            <li>
                <a class="active" data-toggle="tab" href="#cloud">
                    <img src="./static/assets/images/movegroup-tab.png" />
                    <span>GCP Migration Sheet</span>
                </a>
            </li>
            {% else %}
            <li>
                <a data-toggle="tab" href="#data1">
                    <img src="./static/assets/images/cloudendure.png" />
                    <span>AWS Migration Sheet</span>
                </a>
            </li>
             {% endif %}
            <li>
                <a data-toggle="tab" href="#data">
                    <img src="./static/assets/images/buildsheet-tab.png" />
                    <span>Build Sheet</span>
                </a>
            </li>
            <li>
                <a data-toggle="tab" href="#ai">
                    <img src="./static/assets/images/cli-tab.png" />
                    <span>Build VMs</span>
                </a>
            </li>
            <li>
                <a data-toggle="tab" href="#movegroup">
                    <img src="./static/assets/images/valtag-48.png" />
                    <span>Movegroup Validation</span>
                </a>
            </li>
        </ul>
    </div>
    <div class="container">
        <div class="tab-content section-padding wow fadeInUp">
            {% include 'migration/_gcp_migration_sheet.html' %}
            {% include 'migration/_aws_migration_sheet.html' %}
            {% include 'migration/_build_sheet.html' %}
            {% include 'migration/_build_vms.html' %}
            {% if provider == 'gcp' %}
            {% include 'migration/_movegroup_validation.html' %}
            {% else %}
            {% include 'migration/_aws_movegroup_validation.html' %}
            {% endif %}
        </div>
    </div>
</div>

<!--Glide-->
<div id="div5" class="hidden">
    <div class="nav-container wow fadeInUp">
        <ul class="nav nav-tabs">
            <li></li>
            <li>
                <a class="active" data-toggle="tab" href="#software12">
                    <img src="./static/assets/images/azinv1.png" />
                    <span>GCP Inventory</span>
                </a>
            </li>
            <li>
                <a data-toggle="tab" href="#aws12">
                    <img src="./static/assets/images/awsinv1.png" />
                    <span>Inventory Import</span>
                </a>
            </li>
            <li>
                <a data-toggle="tab" href="#foundation">
                    <img src="./static/assets/images/foundation.png" />
                    <span>GCP TF Foundation</span>
                </a>
            </li>
            <li></li>
        </ul>
    </div>
    <!--Glide-->
    <div class="container">
        <div class="tab-content section-padding wow fadeInUp">
            {% include 'glide/_gcp_inventory.html' %}
            {% include 'glide/_inventory_import.html' %}
            {% include 'glide/_gcp_tf_foundation.html' %}
        </div>
    </div>
</div>
</div>
{% endblock main %}
