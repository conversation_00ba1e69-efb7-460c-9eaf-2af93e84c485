aws_sheet_names_to_update = {
    "CERTIFICATES": [
        "Migration (Yes/No)",
        "ProjectID",
        "Cert Name",
        "Location",
        "CertificateType",
        "Domains",
        "SubjectAlternativeNames",
        "CertMap",
        "CertMapEntry",
        "SelfManagedCertificate",
        "SelfManagedPrivateKey"
    ],
    "EC2": [
        "Migration (Yes/No)"
        "HostProject"
        "Movegroup"
        "Status" "Instance Name"
        "Project"
        "Region"
        "Zone"
        "VPC"
        "Subnet"
        "Target Internal IP"
        "Network Tag"
        "Service Account"
        "Need for External IP"
        "Labels"
        "Size"
        "Image"
        "ImageProject"
        "Disk"
        "DiskType"
        "SnapshotPolicy"
        "Confidential VM service"
        "Shielded VM"
        "Node Affinity Labels"
        "Minimum vCPUs Allocated"
        "Additional Licenses"
        "IP"
        "Access"
    ],
    "ELASTICACHE_CLUSTER": [
        "Migration (Yes/No)",
        "ProjectID",
        "InstanceID",
        "Target_Region",
        "Zone",
        "RedisVersion",
        "Connections",
        "PSARange",
        "Enable IAM AUTH",
        "EnableInTransitEncryption",
        "Snapshot Interval"
    ],
    "RDS": [
        "Migration (Yes/No)",
        "ProjectID",
        "Instance Name",
        "Host Project",
        "Region",
        "Zone",
        "Edition",
        "DB Version",
        "CPU",
        "Memory (GB)",
        "Storage (GB)",
        "High Availability",
        "Replica",
        "Private Network",
        "PSA Range",
        "Authorized Network",
        "Database Engine",
        "Database Version",
        "Migration Tool",
        "Instance Type",
        "Maintenance Window (Weekly)",
        "Backup Retention (Days)",
        "Point in Time Recovery",
        "Deletion Protection",
        "Query Insights",
        "Automatic Storage Increase",
        "Custom Parameters",
        "Number of Read Replicas"
    ],
    "S3": [
        "Migration (Yes/No)",
        "ProjectID",
        "Bucket Name",
        "Target_Region",  # Updated to match actual header
        "Movegroup",
        "Status",
        "Public Access?",
        "IAM",
        "Scope",
        "Access Control",
        "Retention",
        "Retention Period(sec/days/month/year)",
        "Object Versioning",
        "Max. versions per object",
        "Expire non-current versions after",
        "Soft delete policy (For data recovery)",
        "Lifecycle Policy Rule1",
        "Lifecycle Policy Rule2",
        "Lifecycle Policy Rule3",
        "Storage Class",
        "Labels"
    ],
    "LAMBDA": [
        "Migration (Yes/No)",
        "ProjectID",
        "ProjectName",
        "ServiceName",
        "Target_Region",
        "HostProject",
        "SecretProject",
        "Generation", 
        "Authentication",
        "ServiceAccountEmail",
        "SecurityLevel",
        "SecretEnvVariables",
        "EnvVariables",
        "HTTPS Trigger",
        "VPCConnector",
        "VPCConnectorEgress",
        "HostVPC",
        "Subnet",
        "Ingress Settings",
        "Session Affinity",
        "Container Port",
        "Runtime",
        "EntryPoint",
        "Container Name",
        "Target_Memory",
        "Target_Timeout",
        "Concurrency",
        "MinInstances",
        "MaxInstances",
        "CloudSQL connections",
        "DockerRegistry",
        "SourceUploadUrl",
        "SourceBucket",
        "SourceObject",
        "Labels",
        "Billing",
        "TriggerType",
        "EventTriggers"
    ],
    "SNS": [
        "Migration (Yes/No)",
        "ProjectID",
        "TopicName",
        "SubscriptionName",
        "DeliveryType",
        "MessageRetentionDuration",
        "ExpirationPeriod",
        "RetryPolicy",
        "MaxDeliveryAttempt",
        "DLQ Enable? (yes/no)",
        "DLQ TopicName",
        "ACK Deadline (seconds)",
        "enable_message_ordering",
        "exactly_once_delivery"
    ],
    "ECS_SERVICE": [
        "Migration (Yes/No)",
        "ProjectID",
        "ProjectName",
        "ServiceName",
        "Target_Region",
        "HostProject",
        "SecretProject",
        "Container Image URL",
        "Generation:2", 
        "Authentication",
        "ServiceAccountEmail",
        "SecurityLevel",
        "SecretEnvVariables",
        "EnvVariables",
        "HTTPS Trigger",
        "VPCConnector",
        "VPCConnectorEgress",
        "HostVPC",
        "Subnet",
        "Ingress Settings",
        "Session Affinity",
        "Container Port",
        "Runtime",
        "EntryPoint",
        "Container Name",
        "Memory",
        "Timeout",
        "Concurrency",
        "MinInstances",
        "MaxInstances",
        "CloudSQL connections",
        "DockerRegistry",
        "SourceUploadUrl",
        "SourceBucket",
        "SourceObject",
        "Labels",
        "Billing",
        "TriggerType",
        "EventTriggers"
    ],
    "SQS": [
        "Migration (Yes/No)",
        "ProjectID",
        "QueueName",
        "DeliveryType",
        "MessageRetentionDuration",
        "ExpirationPeriod",
        "RetryPolicy",
        "MaxDeliveryAttempt",
        "DLQ Enable? (yes/no)",
        "DLQ TopicName",
        "ACK Deadline (seconds)",
        "enable_message_ordering",
        "exactly_once_delivery"
    ],
    "ELBV1" : [
        "Migration (Yes/No)",
        "ProjectID",
        "Target_Region",
        "Scope",
        "LB scheme",
        "LB Type",
        "LB Name",
        "Fe Name",
        "Fe Protocol",
        "Fe Ports",
        "IP Type",
        "Static IP Address",
        "FE IP address",
        "Network Tier",
        "SSLCertificate",
        "SSLCertificate Type",
        "Certificate Map",
        "SSL Policy",
        "BE Service Name",
        "Backend Type",
        "BE region",
        "BE protocol",
        "BE ports",
        "BE Balancing Mode",
        "Http timeout",
        "Session affinity",
        "ConnectionDrainTimeoutSec",
        "Enable CDN",
        "Health check protocol",
        "Health check ports",
        "HealthCheckIntervalSec",
        "URL map",
        "HostRules",
        "Path matchers",
        "SecurityPolicy",
        "FirewallRuleTag",
        "LoggingEnabled",
        "LoggingSampleRate",
        "Network",
        "Subnetwork",
        "MaxRatePerEndpoint",
        "FailoverPolicyEnabled"
    ],
    "ELBV2" : [
        "Migration (Yes/No)",
        "ProjectID",
        "Target_Region",
        "Scope",
        "LB scheme",
        "LB Type",
        "LB Name",
        "Fe Name",
        "Fe Protocol",
        "Fe Ports",
        "IP Type",
        "Static IP Address",
        "FE IP address",
        "Network Tier",
        "SSLCertificate",
        "SSLCertificate Type",
        "Certificate Map",
        "SSL Policy",
        "BE Service Name",
        "Backend Type",
        "BE region",
        "BE protocol",
        "BE ports",
        "BE Balancing Mode",
        "Http timeout",
        "Session affinity",
        "ConnectionDrainTimeoutSec",
        "Enable CDN",
        "Health check protocol",
        "Health check ports",
        "HealthCheckIntervalSec",
        "URL map",
        "HostRules",
        "Path matchers",
        "SecurityPolicy",
        "FirewallRuleTag",
        "LoggingEnabled",
        "LoggingSampleRate",
        "Network",
        "Subnetwork",
        "MaxRatePerEndpoint",
        "FailoverPolicyEnabled"
    ]
}

azure_sheet_names_to_update = {
    "Virtual Machines": [
        "Migration (Yes/No)"
        "HostProject"
        "Movegroup"
        "Status" "Instance Name"
        "Project"
        "Region"
        "Zone"
        "VPC"
        "Subnet"
        "Target Internal IP"
        "Network Tag"
        "Service Account"
        "Need for External IP"
        "Labels"
        "Size"
        "Image"
        "ImageProject"
        "Disk"
        "DiskType"
        "SnapshotPolicy"
        "Confidential VM service"
        "Shielded VM"
        "Node Affinity Labels"
        "Minimum vCPUs Allocated"
        "Additional Licenses"
        "IP"
        "Access"
    ],
    "Load Balancers" : [
        "Migration (Yes/No)",
        "ProjectID",
        "Target_Region",
        "Scope",
        "LB scheme",
        "LB Type",
        "LB Name",
        "Fe Name",
        "Fe Protocol",
        "Fe Ports",
        "IP Type",
        "Static IP Address",
        "FE IP address",
        "Network Tier",
        "SSLCertificate",
        "SSLCertificate Type",
        "Certificate Map",
        "SSL Policy",
        "BE Service Name",
        "Backend Type",
        "BE region",
        "BE protocol",
        "BE ports",
        "BE Balancing Mode",
        "Http timeout",
        "Session affinity",
        "ConnectionDrainTimeoutSec",
        "Enable CDN",
        "Health check protocol",
        "Health check ports",
        "HealthCheckIntervalSec",
        "URL map",
        "HostRules",
        "Path matchers",
        "SecurityPolicy",
        "FirewallRuleTag",
        "LoggingEnabled",
        "LoggingSampleRate",
        "Network",
        "Subnetwork",
        "MaxRatePerEndpoint",
        "FailoverPolicyEnabled"
    ],
    "Redis Cache": [
        "Migration (Yes/No)",
        "ProjectID",
        "InstanceID",
        "Target_Region",
        "Zone",
        "RedisVersion",
        "Connections",
        "PSARange",
        "Enable IAM AUTH",
        "EnableInTransitEncryption",
        "Snapshot Interval"
    ],
    "SQL DBs": [
        "Migration (Yes/No)",
        "ProjectID",
        "Instance Name",
        "HostProject",
        "Target_Region",
        "Zone",
        "Edition",
        "DB Version",
        "CPU",
        "Memory(GB)",
        "Storage(GB)",
        "High Availability",
        "Replica",
        "PrivateNetwork",
        "PSARange",
        "Authorizednetwork"
    ],
    "MySQL": [
        "Migration (Yes/No)",
        "ProjectID",
        "Instance Name",
        "HostProject",
        "Target_Region",
        "Zone",
        "Edition",
        "DB Version",
        "CPU",
        "Memory(GB)",
        "Storage(GB)",
        "High Availability",
        "Replica",
        "PrivateNetwork",
        "PSARange",
        "Authorizednetwork"
    ],
    "PostgreSQL": [
        "Migration (Yes/No)",
        "ProjectID",
        "Instance Name",
        "HostProject",
        "Target_Region",
        "Zone",
        "Edition",
        "DB Version",
        "CPU",
        "Memory(GB)",
        "Storage(GB)",
        "High Availability",
        "Replica",
        "PrivateNetwork",
        "PSARange",
        "Authorizednetwork"
    ]
}

gcp_to_aws_mappings = {

    "ssl_certificate": [
    "Migration (Yes/No)",
    "AccountID",
    "Cert Type",
    "Domain Names",
    "Allow export",
    "Key algorithm",
    "Usage Type",
    "Validation method",
    "Certificate status",
    "Managed renewal",
    "Expiration date",
    "Attachment Resource"
  ],

  "compute": [
    "Migration (Yes/No)",
    "AccountId",
    "Instance_Name",
    "Region",
    "Zone",
    "AMI",
    "InstanceType",
    "Target_VPC",
    "Subnet Id",
    "Availability Zone",
    "Firewall (Security groups)",
    "StorageVolumesize",
    "StorageVolume type",
    "Device Name",
    "Snapshot",
    "Domain join directory",
    "IAM instance profile",
    "Hostname type",
    "Instance auto-recovery",
    "Termination protection",
    "CloudWatch monitoring",
    "Placement group",
    "Purchasing option",
    "Tenancy",
    "License configurations",
    "Metadata accessible"
  ],

  "redis": [
    "Migration (Yes/No)",
    "AccountID",
    "InstanceName",
    "TargetRegion",
    "AvailabilityZone",
    "EngineVersion",
    "NodeType",
    "NumberOfNodes",
    "ClusterMode",
    "TransitEncryption",
    "AtRestEncryption",
    "SnapshotRetention"
  ],

  "cloud_sql": [
    "Migration (Yes/No)",
    "AccountID",
    "Region",
    "DBInstanceIdentifier",
    "DBInstanceClass",
    "MultiAZDeployment",
    "Availability Zone",
    "DB Engine",
    "DBEngineVersion",
    "AllocatedStorageGB",
    "StorageType",
    "VPC",
    "DBSubnetGroup",
    "VPCSecurityGroups",
    "PublicAccess",
    "DatabasePort",
    "Encryption",
    "BackupRetentionPeriodDays",
    "PreferredBackupWindow",
    "EnableAutomaticBackups",
    "PerformanceInsights",
    "Monitoring",
    "MonitoringType",
    "PreferredMaintenanceWindow",
    "DeletionProtection",
    "Tags"
  ],

  "storage": [
    "Migration (Yes/No)",
    "AccountID",
    "Bucket Name",
    "Region",
    "Object Ownership",
    "Public Access",
    "Encryption Type",
    "Storage Class",
    "Lifecycle rules",
    "Bucket Versioning",
    "Tags",
    "Bucket Key",
    "Requester Pays",
    "ACLs",
    "Bucket policy",
    "CORS configuration",
    "Object Lock",
    "Replication rules",
    "Server access logging"
  ],
   
  "cloud_function": [
    "Migration (Yes/No)",
    "Account ID",
    "Function name",
    "TargetRuntime",
    "Architecture",
    "Package type",
    "Code location",
    "Handler",
    "Memory (MB)",
    "Timeout (seconds)",
    "Ephemeral storage (MB)",
    "Environment variables",
    "Execution role",
    "VPC",
    "Subnets",
    "Security groups",
    "File system (Amazon EFS)",
    "Layers",
    "Triggers",
    "Reserved concurrency",
    "Provisioned concurrency",
    "Asynchronous invocation - On success destination",
    "Asynchronous invocation - On failure destination",
    "Dead-letter queue ARN",
    "Function URL enabled (Yes/No)",
    "Function URL - Auth type",
    "Enable active tracing (X-Ray)",
    "Log retention (days)",
    "Tags",
    "Deployment package S3 bucket",
    "Deployment package S3 key",
    "Container image URI (if image)",
    "Comments"
  ],

  "cloud_run_service" : [
     
    "Migration (Yes/No)",
    "Account ID",
    "Function name",
    "TargetRuntime",
    "Architecture",
    "Package type",
    "Code location",
    "Handler",
    "Memory (MB)",
    "Timeout (seconds)",
    "Ephemeral storage (MB)",
    "Environment variables",
    "Execution role",
    "VPC",
    "Subnets",
    "Security groups",
    "File system (Amazon EFS)",
    "Layers",
    "Triggers",
    "Reserved concurrency",
    "Provisioned concurrency",
    "Asynchronous invocation - On success destination",
    "Asynchronous invocation - On failure destination",
    "Dead-letter queue ARN",
    "Function URL enabled (Yes/No)",
    "Function URL - Auth type",
    "Enable active tracing (X-Ray)",
    "Log retention (days)",
    "Tags",
    "Deployment package S3 bucket",
    "Deployment package S3 key",
    "Container image URI (if image)",
    "Comments"
  ],
  "pubsub_topic": [
    "Migration (Yes/No)",
    "Account ID",
    "Display Name",
    "Topic name",
    "AWS Region",
    "TopicType",
    "Encryption",
    "AWS KMS key",
    "Access policy",
    "Delivery status logging",
    "Tags"
  ],
  "pubsub": [
    "Migration (Yes/No)",
    "Account ID",
    "Subscription name",
    "Attached topic",
    "Protocol",
    "Target Endpoint",
    "Filter policy",
    "DLQ ARN",
    "Subscription delivery policy",
    "Queue Name",
    "Queue Type",
    "Delivery Delay (seconds)",
    "Maximum Message Size (bytes)",
    "Message Retention Period (seconds/days)",
    "Visibility Timeout (seconds)",
    "Receive Message Wait Time (seconds)",
    "Dead Letter Queue Enabled (Yes/No)",
    "Dead Letter Queue Name (if enabled)",
    "Maximum Receives Before DLQ",
    "Encryption (AWS SSE | KMS Key)",
    "Access Policy (Public/Private/Custom IAM)",
    "Redrive allow policy queues",
    "DLQ",
    "Maximum receives",
    "Tags"
  ]
}

azure_to_aws_mappings = {


  "Virtual Machines": [
    "Migration (Yes/No)",
    "AccountId",
    "Instance_Name",
    "Region",
    "Zone",
    "AMI",
    "InstanceType",
    "Target_VPC",
    "Subnet Id",
    "Target Availability Zone",
    "Firewall (Security groups)",
    "StorageVolumesize",
    "StorageVolume type",
    "Device Name",
    "Snapshot",
    "Domain join directory",
    "IAM instance profile",
    "Hostname type",
    "Instance auto-recovery",
    "Termination protection",
    "CloudWatch monitoring",
    "Placement group",
    "Purchasing option",
    "Tenancy",
    "License configurations",
    "Metadata accessible"
  ],

  "Redis Cache": [
    "Migration (Yes/No)",
    "AccountID",
    "InstanceName",
    "TargetRegion",
    "AvailabilityZone",
    "EngineVersion",
    "NodeType",
    "NumberOfNodes",
    "ClusterMode",
    "TransitEncryption",
    "AtRestEncryption",
    "SnapshotRetention"
  ],

  "PostgreSQL": [
    "Migration (Yes/No)",
    "AccountID",
    "Region",
    "DBInstanceIdentifier",
    "DBInstanceClass",
    "MultiAZDeployment",
    "Availability Zone",
    "DB Engine",
    "DBEngineVersion",
    "AllocatedStorageGB",
    "StorageType",
    "VPC",
    "DBSubnetGroup",
    "VPCSecurityGroups",
    "PublicAccess",
    "DatabasePort",
    "Encryption",
    "BackupRetentionPeriodDays",
    "PreferredBackupWindow",
    "EnableAutomaticBackups",
    "PerformanceInsights",
    "Monitoring",
    "MonitoringType",
    "PreferredMaintenanceWindow",
    "DeletionProtection",
    "Tags"
  ],

  "MySQL": [
    "Migration (Yes/No)",
    "AccountID",
    "Region",
    "DBInstanceIdentifier",
    "DBInstanceClass",
    "MultiAZDeployment",
    "Availability Zone",
    "DB Engine",
    "DBEngineVersion",
    "AllocatedStorageGB",
    "StorageType",
    "VPC",
    "DBSubnetGroup",
    "VPCSecurityGroups",
    "PublicAccess",
    "DatabasePort",
    "Encryption",
    "BackupRetentionPeriodDays",
    "PreferredBackupWindow",
    "EnableAutomaticBackups",
    "PerformanceInsights",
    "Monitoring",
    "MonitoringType",
    "PreferredMaintenanceWindow",
    "DeletionProtection",
    "Tags"
  ],

  "SQL DBs": [
    "Migration (Yes/No)",
    "AccountID",
    "Region",
    "DBInstanceIdentifier",
    "DBInstanceClass",
    "MultiAZDeployment",
    "Availability Zone",
    "DB Engine",
    "DBEngineVersion",
    "AllocatedStorageGB",
    "StorageType",
    "VPC",
    "DBSubnetGroup",
    "VPCSecurityGroups",
    "PublicAccess",
    "DatabasePort",
    "Encryption",
    "BackupRetentionPeriodDays",
    "PreferredBackupWindow",
    "EnableAutomaticBackups",
    "PerformanceInsights",
    "Monitoring",
    "MonitoringType",
    "PreferredMaintenanceWindow",
    "DeletionProtection",
    "Tags"
  ],

  "Storage Accounts": [
    "Migration (Yes/No)",
    "AccountID",
    "Bucket Name",
    "Region",
    "Object Ownership",
    "Public Access",
    "Encryption Type",
    "Storage Class",
    "Lifecycle rules",
    "Bucket Versioning",
    "Tags",
    "Bucket Key",
    "Requester Pays",
    "ACLs",
    "Bucket policy",
    "CORS configuration",
    "Object Lock",
    "Replication rules",
    "Server access logging"
  ],
 
  "Load Balancers": [
         "Migration (Yes/No)",
        "AccountID",
        "Region",
        "Load balancer name",
        "Load balancer type",
        "Scheme",
        "VPC ID",
        "Availability Zones",
        "Security groups",
        "IP address type",
        "Target group name",
        "Target group ARN",
        "Target type",
        "Protocol",
        "Port",
        "Health check protocol",
        "Health check port",
        "Health check path"
  ]
 
 
}