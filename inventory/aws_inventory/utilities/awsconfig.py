import boto3
import google.auth.transport.requests
import google.auth.exceptions
from google.auth import compute_engine # Specifically for getting ID tokens on GCE
from botocore.exceptions import ClientError
from typing import Tuple, Optional, Dict, Any


def assume_aws_role_with_google_id_token(
    role_arn: str,
    role_session_name: str = 'google-identity-session',
    duration_seconds: int = 3600,
    target_audience: str = 'https://www.googleapis.com/auth/cloud-platform'
) -> Tuple[Optional[Dict[str, Any]], Optional[Exception]]:
    """
    Assumes an AWS IAM role using a Google Cloud ID Token from GCE metadata. (Silent version)

    This version avoids logging any information during its execution. It catches
    exceptions internally and returns them as part of the tuple, rather than raising them.
    Designed for environments where logging function activity is undesirable due to sensitivity.

    Args:
        role_arn: The ARN of the AWS IAM role to assume.
        role_session_name: The name to associate with the temporary AWS session.
        duration_seconds: The duration, in seconds, for which the credentials should be valid
                          (between 900 and 3600 seconds, default: 3600).
        target_audience: The audience claim for the Google ID token. This **must** match the
                         audience configured in the AWS IAM OIDC identity provider setup for Google.

    Returns:
        A tuple containing:
        - A dictionary with the temporary AWS credentials ('AccessKeyId', 'SecretAccessKey',
          'SessionToken', 'Expiration') if successful, otherwise None.
        - The Exception object if an error occurred during the process, otherwise None.
    """
    credentials_result: Optional[Dict[str, Any]] = None
    error_result: Optional[Exception] = None

    try:
        # 1. Obtain Google ID Token (specifically from GCE Metadata Server)
        auth_request = google.auth.transport.requests.Request()
        credentials = compute_engine.IDTokenCredentials(
            request=auth_request,
            target_audience=target_audience,
            use_metadata_identity_endpoint=True
        )
        credentials.refresh(auth_request)
        id_token = credentials.token

        # Handle unlikely case of empty token after successful refresh without raising
        if not id_token:
            error_result = ValueError("Fetched Google ID Token is empty but no error was raised during refresh.")
            # No logging here
        else:
            # No logging here either

            # 2. Assume AWS Role using the obtained Google ID Token
            sts_client = boto3.client('sts')
            response = sts_client.assume_role_with_web_identity(
                RoleArn=role_arn,
                RoleSessionName=role_session_name,
                WebIdentityToken=id_token,
                DurationSeconds=duration_seconds
            )
            # No logging on success
            credentials_result = response.get('Credentials') # Success case
 
    except Exception as e:
        # No logging
        error_result = e

    # Return the results captured
    return credentials_result, error_result